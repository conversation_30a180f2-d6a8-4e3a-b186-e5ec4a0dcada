const crypto = require('crypto');

/**
 * Generate random booking ID dengan format: BK-YYYYMMDD-XXXXX
 * Contoh: BK-20250803-A1B2C
 */
function generateBookingId() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const dateStr = `${year}${month}${day}`;
  
  // Generate 5 karakter random alphanumeric
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let randomStr = '';
  for (let i = 0; i < 5; i++) {
    randomStr += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return `BK-${dateStr}-${randomStr}`;
}

/**
 * Generate kode aktivitas berurutan untuk satu booking
 * Format: act001, act002, act003, dst
 * @param {number} sequence - nomor urut aktivitas dalam booking
 */
function generateActivityCode(sequence) {
  return `act${String(sequence).padStart(3, '0')}`;
}

/**
 * Get next activity sequence number untuk booking tertentu
 * @param {Object} pb - PocketBase client
 * @param {string} bookingId - ID booking
 */
async function getNextActivitySequence(pb, bookingId) {
  try {
    // Cari aktivitas terakhir untuk booking ini
    const activities = await pb.collection('booking_activities').getList(1, 1, {
      filter: `booking_id = "${bookingId}"`,
      sort: '-code_activity'
    });
    
    if (activities.items.length === 0) {
      return 1; // Aktivitas pertama
    }
    
    // Extract nomor dari kode aktivitas terakhir (contoh: act003 -> 3)
    const lastCode = activities.items[0].code_activity;
    const lastNumber = parseInt(lastCode.replace('act', ''));
    
    return lastNumber + 1;
  } catch (error) {
    console.error('Error getting next activity sequence:', error);
    return 1; // Default ke 1 jika ada error
  }
}

module.exports = {
  generateBookingId,
  generateActivityCode,
  getNextActivitySequence
};
